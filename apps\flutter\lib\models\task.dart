/// Task model for the Drift system
class TaskModel {
  final String id;
  final String title;
  final String description;
  final bool done;
  final DateTime createdAt;

  const TaskModel({
    required this.id,
    required this.title,
    required this.description,
    required this.done,
    required this.createdAt,
  });

  /// Create a Task from reconstructed fields
  factory TaskModel.fromFields(String id, Map<String, String?> fields, DateTime createdAt) {
    return TaskModel(
      id: id,
      title: fields['title'] ?? '',
      description: fields['description'] ?? '',
      done: fields['done'] == 'true',
      createdAt: createdAt,
    );
  }

  @override
  String toString() {
    return 'Task(id: $id, title: $title, done: $done)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
