import 'dart:async';
import 'dart:convert';

import 'package:core/core.dart';
import 'package:http/http.dart' as http;
import 'package:sqflite/sqflite.dart';

/// Manages synchronization between nodes
class SyncManager {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  final TailscaleClient? _tailscaleClient;

  Timer? _syncTimer;
  List<Node> _knownNodes = [];
  bool _isInitialized = false;

  static const Duration _syncInterval = Duration(seconds: 15);
  static const Duration _nodeDiscoveryInterval = Duration(minutes: 1);

  SyncManager({
    required DatabaseManager databaseManager,
    required String nodeId,
    required int apiPort,
    String? tailscaleAccessToken,
    String? tailnet,
  })  : _databaseManager = databaseManager,
        _nodeId = nodeId,
        _tailscaleClient = tailscaleAccessToken != null
            ? TailscaleClient(
                accessToken: tailscaleAccessToken,
                tailnet: tailnet ??
                    TailscaleClient.extractTailnet(
                        tailscaleAccessToken, tailnet),
              )
            : null;

  /// Initialize the sync manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Test Tailscale connection if configured
    if (_tailscaleClient != null) {
      final connected = await _tailscaleClient!.testConnection();
      if (connected) {
        print('Tailscale API connection successful');
        await _discoverNodes();
      } else {
        print('Warning: Tailscale API connection failed');
      }
    } else {
      print('Tailscale not configured, running in local mode');
    }

    // Start periodic sync
    _startPeriodicSync();

    _isInitialized = true;
  }

  /// Shutdown the sync manager
  Future<void> shutdown() async {
    _syncTimer?.cancel();
    _syncTimer = null;
    _isInitialized = false;
  }

  /// Start periodic synchronization
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(_syncInterval, (_) async {
      await _performSync();
    });

    // Also perform node discovery periodically
    Timer.periodic(_nodeDiscoveryInterval, (_) async {
      await _discoverNodes();
    });
  }

  /// Discover nodes using Tailscale API
  Future<void> _discoverNodes() async {
    if (_tailscaleClient == null) return;

    try {
      final nodes = await _tailscaleClient!.getDevices();

      // Filter out this node
      _knownNodes = nodes.where((node) => node.id != _nodeId).toList();

      print(
          'Discovered ${_knownNodes.length} nodes: ${_knownNodes.map((n) => n.name).join(', ')}');
    } catch (e) {
      print('Failed to discover nodes: $e');
    }
  }

  /// Perform synchronization with all known nodes
  Future<void> _performSync() async {
    if (_knownNodes.isEmpty) return;

    try {
      // Get unsynced events to send
      final unsyncedEvents = await _databaseManager.getUnsyncedEvents();

      final List<String> syncedEventIds = [];

      for (final node in _knownNodes) {
        try {
          // Send events to node
          if (unsyncedEvents.isNotEmpty) {
            await _sendEventsToNode(node, unsyncedEvents);
            syncedEventIds.addAll(unsyncedEvents.map((e) => e.id));
          }

          // Receive events from node
          await _receiveEventsFromNode(node);
        } catch (e) {
          print('Failed to sync with node ${node.name}: $e');
        }
      }

      // Mark successfully sent events as synced
      if (syncedEventIds.isNotEmpty) {
        await _databaseManager.markEventsSynced(syncedEventIds);
      }
    } catch (e) {
      print('Sync error: $e');
    }
  }

  /// Push events and records to a specific node
  Future<void> _sendEventsToNode(Node node, List<Event> events) async {
    if (events.isEmpty) return;

    // Get records for these events
    final List<Record> records = [];
    for (final event in events) {
      final eventRecords = await _databaseManager.getRecordsByEventId(event.id);
      records.addAll(eventRecords);
    }

    final url = '${node.baseUrl}/push';

    final response = await http
        .post(
          Uri.parse(url),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'events': events.map((e) => e.toJson()).toList(),
            'records': records.map((r) => r.toJson()).toList(),
            'sender_node_id': _nodeId,
          }),
        )
        .timeout(const Duration(seconds: 10));

    if (response.statusCode != 200) {
      throw Exception(
          'Failed to push events to ${node.name}: ${response.statusCode}');
    }
  }

  /// Pull events and records from a specific node
  Future<void> _receiveEventsFromNode(Node node, {int? since}) async {
    final url = '${node.baseUrl}/pull';
    final uri = since != null
        ? Uri.parse(url).replace(queryParameters: {'since': since.toString()})
        : Uri.parse(url);

    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    ).timeout(const Duration(seconds: 10));

    if (response.statusCode != 200) {
      throw Exception(
          'Failed to pull from ${node.name}: ${response.statusCode}');
    }

    final data = jsonDecode(response.body) as Map<String, dynamic>;
    final eventsData = data['events'] as List<dynamic>? ?? [];
    final recordsData = data['records'] as List<dynamic>? ?? [];

    if (eventsData.isNotEmpty || recordsData.isNotEmpty) {
      final events = eventsData
          .map((eventData) => Event.fromJson(eventData as Map<String, dynamic>))
          .toList();

      final records = recordsData
          .map((recordData) => Record.fromJson(recordData as Map<String, dynamic>))
          .toList();

      if (eventsData.isNotEmpty || recordsData.isNotEmpty) {
        // Get existing event IDs to avoid duplicates
        final existingEventIds = await _getExistingEventIds();

        // Filter events: keep all events that don't exist in our database
        final newEvents = events
            .where((event) => !existingEventIds.contains(event.id))
            .toList();

        // Filter records: keep records for events we're adding
        final newEventIds = newEvents.map((e) => e.id).toSet();
        final newRecords = records
            .where((record) => newEventIds.contains(record.eventId))
            .toList();

        if (newEvents.isNotEmpty || newRecords.isNotEmpty) {
          // Insert events and records in a transaction
          await _databaseManager.database.transaction((txn) async {
            for (final event in newEvents) {
              await txn.insert(
                'events',
                event.toMap(),
                conflictAlgorithm: ConflictAlgorithm.ignore, // Ignore if already exists
              );
            }

            for (final record in newRecords) {
              await txn.insert(
                'records',
                record.toMap(),
                conflictAlgorithm: ConflictAlgorithm.ignore, // Ignore if already exists
              );
            }
          });

          print('Pulled ${newEvents.length} new events and ${newRecords.length} new records from ${node.name}');
        } else {
          print('No new data to pull from ${node.name}');
        }
      }
    }
  }

  /// Manually trigger synchronization
  Future<void> syncNow() async {
    await _performSync();
  }

  /// Pull from all connected nodes (for refresh button)
  Future<void> pullFromAllNodes({int? since}) async {
    if (_knownNodes.isEmpty) {
      print('No known nodes to pull from');
      return;
    }

    print('Pulling from ${_knownNodes.length} nodes...');

    for (final node in _knownNodes) {
      try {
        await _receiveEventsFromNode(node, since: since);
      } catch (e) {
        print('Failed to pull from node ${node.name}: $e');
        // Continue with other nodes even if one fails
      }
    }

    print('Finished pulling from all nodes');
  }

  /// Push a specific event with its records to all nodes
  Future<void> pushEventToAllNodes(Event event, List<Record> records) async {
    if (_knownNodes.isEmpty) {
      print('No known nodes to push to');
      return;
    }

    print('Pushing event ${event.id} to ${_knownNodes.length} nodes...');

    for (final node in _knownNodes) {
      try {
        final url = '${node.baseUrl}/push';

        final response = await http
            .post(
              Uri.parse(url),
              headers: {'Content-Type': 'application/json'},
              body: jsonEncode({
                'events': [event.toJson()],
                'records': records.map((r) => r.toJson()).toList(),
                'sender_node_id': _nodeId,
              }),
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          print('Successfully pushed event to ${node.name}');
        } else {
          print('Failed to push event to ${node.name}: ${response.statusCode}');
        }
      } catch (e) {
        print('Failed to push event to node ${node.name}: $e');
        // Continue with other nodes even if one fails
      }
    }

    print('Finished pushing event to all nodes');
  }

  /// Add a node manually (for testing or local network discovery)
  void addNode(Node node) {
    if (!_knownNodes.any((n) => n.id == node.id)) {
      _knownNodes.add(node);
    }
  }

  /// Remove a node
  void removeNode(String nodeId) {
    _knownNodes.removeWhere((node) => node.id == nodeId);
  }

  /// Get list of known nodes
  List<Node> get knownNodes => List.unmodifiable(_knownNodes);

  /// Update the list of known nodes (called from TailscaleProvider)
  void updateKnownNodes(List<Node> nodes) {
    // Filter out this node based on current device ID
    _knownNodes = nodes.where((node) => node.id != _nodeId).toList();
    print('Updated known nodes: ${_knownNodes.length} nodes available');
  }

  /// Get existing event IDs from database
  Future<Set<String>> _getExistingEventIds() async {
    final result = await _databaseManager.database.query(
      'events',
      columns: ['id'],
    );
    return result.map((row) => row['id'] as String).toSet();
  }

  /// Check if a node is reachable
  Future<bool> isNodeReachable(Node node) async {
    try {
      final response = await http.get(
        Uri.parse('${node.baseUrl}/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
