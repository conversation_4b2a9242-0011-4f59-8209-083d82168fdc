/// Represents a record in the system
class Record {
  final String eventId;
  final String fieldId;
  final String? value;
  final DateTime createdAt;

  const Record({
    required this.eventId,
    required this.fieldId,
    this.value,
    required this.createdAt,
  });

  /// Create a Record from a database row
  factory Record.fromMap(Map<String, dynamic> map) {
    return Record(
      eventId: map['event_id'] as String,
      fieldId: map['field_id'] as String,
      value: map['value'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
    );
  }

  /// Convert the record to a database row
  Map<String, dynamic> toMap() {
    return {
      'event_id': eventId,
      'field_id': fieldId,
      'value': value,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Convert to JSON for API transmission
  Map<String, dynamic> toJson() {
    return {
      'event_id': eventId,
      'field_id': fieldId,
      'value': value,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000, // UTC timestamp in seconds
    };
  }

  /// Create from JSON received from API
  factory Record.fromJson(Map<String, dynamic> json) {
    final createdAtValue = json['created_at'];
    DateTime createdAt;

    if (createdAtValue is int) {
      // Check if it's in seconds or milliseconds
      if (createdAtValue > 1000000000000) {
        // Milliseconds
        createdAt = DateTime.fromMillisecondsSinceEpoch(createdAtValue, isUtc: true);
      } else {
        // Seconds
        createdAt = DateTime.fromMillisecondsSinceEpoch(createdAtValue * 1000, isUtc: true);
      }
    } else {
      createdAt = DateTime.now().toUtc();
    }

    return Record(
      eventId: json['event_id'] as String,
      fieldId: json['field_id'] as String,
      value: json['value'] as String?,
      createdAt: createdAt,
    );
  }

  @override
  String toString() {
    return 'Record(eventId: $eventId, fieldId: $fieldId, value: $value, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Record &&
           other.eventId == eventId &&
           other.fieldId == fieldId;
  }

  @override
  int get hashCode => Object.hash(eventId, fieldId);
}

/// Represents an event in the system
class Event {
  final String id;
  final String origin;
  final DateTime timestamp;
  final String entityType;
  final String entityId;
  final String eventType;
  final Map<String, dynamic> payload;
  final bool synced;

  const Event({
    required this.id,
    required this.origin,
    required this.timestamp,
    required this.entityType,
    required this.entityId,
    required this.eventType,
    required this.payload,
    this.synced = false,
  });

  /// Create an Event from a database row
  factory Event.fromMap(Map<String, dynamic> map) {
    return Event(
      id: map['id'] as String,
      origin: map['node'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      entityType: map['type'] as String,
      entityId: map['entity_id'] as String,
      eventType: map['type'] as String, // Use type as eventType for now
      payload: {},
      synced: false,
    );
  }

  /// Convert the event to a database row
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': eventType,
      'entity_id': entityId,
      'node': origin,
      'created_at': timestamp.millisecondsSinceEpoch,
    };
  }

  /// Create a copy with updated fields
  Event copyWith({
    String? id,
    String? origin,
    DateTime? timestamp,
    String? entityType,
    String? entityId,
    String? eventType,
    Map<String, dynamic>? payload,
    bool? synced,
  }) {
    return Event(
      id: id ?? this.id,
      origin: origin ?? this.origin,
      timestamp: timestamp ?? this.timestamp,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      eventType: eventType ?? this.eventType,
      payload: payload ?? this.payload,
      synced: synced ?? this.synced,
    );
  }

  /// Convert to JSON for API transmission
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'origin': origin,
      'timestamp': timestamp.millisecondsSinceEpoch ~/ 1000, // UTC timestamp in seconds
      'entity_type': entityType,
      'entity_id': entityId,
      'event_type': eventType,
      'payload': payload,
      'synced': synced,
    };
  }

  /// Create from JSON received from API
  factory Event.fromJson(Map<String, dynamic> json) {
    final timestampValue = json['timestamp'];
    DateTime timestamp;

    if (timestampValue is int) {
      // Timestamp in seconds, convert to DateTime
      timestamp = DateTime.fromMillisecondsSinceEpoch(timestampValue * 1000, isUtc: true);
    } else if (timestampValue is String) {
      // ISO string, parse directly
      timestamp = DateTime.parse(timestampValue);
    } else {
      timestamp = DateTime.now().toUtc();
    }

    return Event(
      id: json['id'] as String,
      origin: json['origin'] as String,
      timestamp: timestamp,
      entityType: json['entity_type'] as String,
      entityId: json['entity_id'] as String,
      eventType: json['event_type'] as String,
      payload: json['payload'] as Map<String, dynamic>? ?? {},
      synced: json['synced'] as bool? ?? false,
    );
  }

  @override
  String toString() {
    return 'Event(id: $id, origin: $origin, timestamp: $timestamp, '
        'entityType: $entityType, entityId: $entityId, eventType: $eventType, '
        'payload: $payload, synced: $synced)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Event && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
