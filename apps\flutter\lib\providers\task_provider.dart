import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:uuid/uuid.dart';

import '../models/task.dart';

/// Provider for managing task state in Flutter
class TaskProvider extends ChangeNotifier {
  final Core _core;
  final List<TaskModel> _tasks = [];
  bool _isLoading = false;
  String? _error;
  
  TaskProvider(this._core) {
    _initialize();
  }
  
  /// Get the list of tasks
  List<TaskModel> get tasks => List.unmodifiable(_tasks);
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;
  
  /// Get node ID
  String get nodeId => _core.nodeId;

  /// Get known nodes count
  int get knownNodesCount => _core.sync.knownNodes.length;
  
  /// Initialize the provider
  Future<void> _initialize() async {
    await _loadTasks();
    
    // Listen to new events from the event store
    // Note: This would need to be implemented in the EventStore
    // _driftCore.database.eventStream.listen(_onNewEvent);
  }
  
  /// Load tasks from the database
  Future<void> _loadTasks() async {
    _setLoading(true);
    _setError(null);

    try {
      final db = _core.database.database;

      // Get all task entities
      final entityResults = await db.query(
        'entities',
        where: 'type_id = ?',
        whereArgs: ['task'],
        orderBy: 'created_at ASC',
      );

      _tasks.clear();

      for (final entityRow in entityResults) {
        final entityId = entityRow['id'] as String;
        final createdAt = DateTime.fromMillisecondsSinceEpoch(entityRow['created_at'] as int);

        // Get all records for this entity through events, ordered by most recent first
        final recordResults = await db.rawQuery('''
          SELECT r.field_id, r.value, r.created_at
          FROM records r
          JOIN events e ON r.event_id = e.id
          WHERE e.entity_id = ?
          ORDER BY r.created_at DESC
        ''', [entityId]);

        // Reconstruct task fields from records (most recent value for each field)
        final fields = <String, String?>{};
        final processedFields = <String>{};

        for (final recordRow in recordResults) {
          final fieldId = recordRow['field_id'] as String;

          // Only use the most recent record for each field
          if (!processedFields.contains(fieldId)) {
            fields[fieldId] = recordRow['value'] as String?;
            processedFields.add(fieldId);
          }
        }

        // Create task from reconstructed fields
        final task = TaskModel.fromFields(entityId, fields, createdAt);
        _tasks.add(task);
      }

    } catch (e) {
      _setError('Failed to load tasks: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Create a new task
  Future<void> createTask(String title, String description) async {
    _setLoading(true);
    _setError(null);

    try {
      const uuid = Uuid();
      final taskId = uuid.v4();
      final eventId = uuid.v4();
      final now = DateTime.now().millisecondsSinceEpoch;
      final db = _core.database.database;

      // Create entity
      await db.insert('entities', {
        'id': taskId,
        'type_id': 'task',
        'created_at': now,
      });

      // Create event
      await db.insert('events', {
        'id': eventId,
        'type': 'create',
        'entity_id': taskId,
        'node': _core.nodeId,
        'created_at': now,
      });

      // Create records for each field
      final records = <Map<String, dynamic>>[];

      if (title.isNotEmpty) {
        final recordData = {
          'event_id': eventId,
          'field_id': 'title',
          'value': title,
          'created_at': now,
        };
        await db.insert('records', recordData);
        records.add(recordData);
      }

      if (description.isNotEmpty) {
        final recordData = {
          'event_id': eventId,
          'field_id': 'description',
          'value': description,
          'created_at': now,
        };
        await db.insert('records', recordData);
        records.add(recordData);
      }

      // Set initial done state to false
      final doneRecordData = {
        'event_id': eventId,
        'field_id': 'done',
        'value': 'false',
        'created_at': now,
      };
      await db.insert('records', doneRecordData);
      records.add(doneRecordData);

      // Reload tasks
      await _loadTasks();

      // Push the event and its records to all nodes
      final event = Event(
        id: eventId,
        origin: _core.nodeId,
        timestamp: DateTime.fromMillisecondsSinceEpoch(now),
        entityType: 'task',
        entityId: taskId,
        eventType: 'create',
        payload: {},
        synced: false,
      );

      final recordObjects = records.map((r) => Record(
        eventId: r['event_id'] as String,
        fieldId: r['field_id'] as String,
        value: r['value'] as String?,
        createdAt: DateTime.fromMillisecondsSinceEpoch(r['created_at'] as int),
      )).toList();

      await _core.sync.pushEventToAllNodes(event, recordObjects);

    } catch (e) {
      _setError('Failed to create task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Complete a task
  Future<void> completeTask(String taskId) async {
    _setLoading(true);
    _setError(null);

    try {
      const uuid = Uuid();
      final eventId = uuid.v4();
      final now = DateTime.now().millisecondsSinceEpoch;
      final db = _core.database.database;

      // Create event
      await db.insert('events', {
        'id': eventId,
        'type': 'update',
        'entity_id': taskId,
        'node': _core.nodeId,
        'created_at': now,
      });

      // Create record to mark task as done
      final recordData = {
        'event_id': eventId,
        'field_id': 'done',
        'value': 'true',
        'created_at': now,
      };
      await db.insert('records', recordData);

      // Reload tasks
      await _loadTasks();

      // Push the event and its record to all nodes
      final event = Event(
        id: eventId,
        origin: _core.nodeId,
        timestamp: DateTime.fromMillisecondsSinceEpoch(now),
        entityType: 'task',
        entityId: taskId,
        eventType: 'update',
        payload: {},
        synced: false,
      );

      final record = Record(
        eventId: recordData['event_id'] as String,
        fieldId: recordData['field_id'] as String,
        value: recordData['value'] as String?,
        createdAt: DateTime.fromMillisecondsSinceEpoch(recordData['created_at'] as int),
      );

      await _core.sync.pushEventToAllNodes(event, [record]);

    } catch (e) {
      _setError('Failed to complete task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Refresh tasks and sync (pull from all connected devices)
  Future<void> refresh() async {
    _setLoading(true);
    _setError(null);

    try {
      // Pull from all known nodes
      await _core.sync.pullFromAllNodes();

      // Reload tasks to reflect any new data
      await _loadTasks();
    } catch (e) {
      _setError('Failed to refresh: $e');
    } finally {
      _setLoading(false);
    }
  }
  

  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  

}
