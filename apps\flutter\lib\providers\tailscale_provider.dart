import 'package:flutter/foundation.dart';
import 'package:core/core.dart';

/// Provider for managing Tailscale configuration and device discovery
class <PERSON><PERSON><PERSON><PERSON>rov<PERSON> extends ChangeNotifier {
  final Core _core;

  String _token = '';
  String? _currentDeviceId;
  List<Node> _devices = [];
  bool _isLoading = false;
  String? _error;

  TailscaleProvider(this._core) {
    _loadConfiguration();
  }

  /// Get current token (masked for display)
  String get tokenMasked =>
      _token.isNotEmpty ? '${_token.substring(0, 8)}...' : '';

  /// Get current device ID
  String? get currentDeviceId => _currentDeviceId;

  /// Get list of devices
  List<Node> get devices => List.unmodifiable(_devices);

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Check if configuration is complete
  bool get isConfigured => _token.isNotEmpty && _currentDeviceId != null;

  /// Load configuration from database
  Future<void> _loadConfiguration() async {
    try {
      final profile = await _core.database.getProfile();
      if (profile != null) {
        _currentDeviceId = profile['tailscale_id'] as String?;
        _token = profile['tailscale_token'] as String? ?? '';
        notifyListeners();

        // Load devices if token is configured
        if (_token.isNotEmpty) {
          await _loadDevices();
        }
      }
    } catch (e) {
      _setError('Failed to load configuration: $e');
    }
  }

  /// Update Tailscale token and load devices for selection
  Future<List<Node>> updateToken(String token) async {
    _setLoading(true);
    _setError(null);

    try {
      _token = token;

      // Load devices with new token
      await _loadDevices();

      return _devices;
    } catch (e) {
      _setError('Failed to load devices: $e');
      return [];
    } finally {
      _setLoading(false);
    }
  }

  /// Select current device and save configuration
  Future<void> selectCurrentDevice(String deviceId) async {
    try {
      // Save to database
      await _core.database.updateProfile(
        tailscaleId: deviceId,
        tailscaleToken: _token,
      );

      _currentDeviceId = deviceId;
      notifyListeners();
    } catch (e) {
      _setError('Failed to save device selection: $e');
    }
  }

  /// Load devices from Tailscale API
  Future<void> _loadDevices() async {
    if (_token.isEmpty) return;

    _setLoading(true);
    _setError(null);

    try {
      final client = TailscaleClient(
        accessToken: _token,
        tailnet: "-", // Use default tailnet
      );

      _devices = await client.getDevices();

      // Update known nodes in sync manager
      _core.updateKnownNodes(_devices);
    } catch (e) {
      _setError('Failed to load devices: $e');
      _devices = [];

      // Clear known nodes on error
      _core.updateKnownNodes([]);
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh devices list
  Future<void> refreshDevices() async {
    await _loadDevices();
  }

  /// Test connection to Tailscale API
  Future<bool> testConnection() async {
    if (_token.isEmpty) return false;

    try {
      final client = TailscaleClient(
        accessToken: _token,
        tailnet: "-", // Use default tailnet
      );

      return await client.testConnection();
    } catch (e) {
      return false;
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Clear all configuration and data
  Future<void> wipeAllData() async {
    _setLoading(true);
    _setError(null);

    try {
      await _core.wipeAllData();

      // Reset local state
      _token = '';
      _currentDeviceId = null;
      _devices = [];

      notifyListeners();
    } catch (e) {
      _setError('Failed to wipe data: $e');
    } finally {
      _setLoading(false);
    }
  }
}
