import 'dart:async';
import 'dart:io';

import 'package:core/core.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'migration_manager.dart';

/// Manages the SQLite database and migrations using sqflite
class DatabaseManager {
  Database? _database;

  /// Get the database instance
  Database get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  Future<void> initialize() async {
    if (_database != null) return;

    // Initialize sqflite for desktop platforms (Windows, Linux, macOS)
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final directory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(directory.path, Configuration.databaseName);

    try {
      _database = await openDatabase(
        dbPath,
        version: Configuration.databaseVersion,
        onCreate: (database, version) async {
          await applyMigrations(database, 0, version);
        },
        onUpgrade: (database, oldVersion, newVersion) async {
          await applyMigrations(database, oldVersion, newVersion);
        },
      );
      print('Database opened successfully at: $dbPath');
    } catch (e) {
      print('Error opening database at $dbPath: $e');
      rethrow;
    }
  }

  /// Insert a new event
  Future<void> insertEvent(Event event) async {
    final eventMap = event.toMap();
    await database.insert(
      'events',
      eventMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Insert multiple events in a transaction
  Future<void> insertEvents(List<Event> events) async {
    await database.transaction((txn) async {
      for (final event in events) {
        final eventMap = event.toMap();
        await txn.insert(
          'events',
          eventMap,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// Get events by entity
  Future<List<Event>> getEventsByEntity(
    String entityType,
    String entityId,
  ) async {
    final result = await database.query(
      'events',
      where: 'type = ? AND entity_id = ?',
      whereArgs: [entityType, entityId],
      orderBy: 'created_at ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get unsynced events
  Future<List<Event>> getUnsyncedEvents({String? since}) async {
    final result = await database.query(
      'events',
      where: 'created_at > ?',
      whereArgs: [since ?? 0],
      orderBy: 'created_at ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Mark events as synced
  Future<void> markEventsSynced(List<String> eventIds) async {
    if (eventIds.isEmpty) return;

    await database.transaction((txn) async {
      for (final eventId in eventIds) {
        await txn.update(
          'events',
          {'synced': 1},
          where: 'id = ?',
          whereArgs: [eventId],
        );
      }
    });
  }

  /// Get events after timestamp
  Future<List<Event>> getEventsAfterTimestamp(String timestamp) async {
    final result = await database.query(
      'events',
      where: 'timestamp > ?',
      whereArgs: [timestamp],
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get all events (for debugging/export)
  Future<List<Event>> getAllEvents() async {
    final result = await database.query(
      'events',
      orderBy: 'timestamp ASC',
    );
    return result.map((row) => Event.fromMap(row)).toList();
  }

  /// Get database file path for sharing
  Future<String> getDatabasePath() async {
    final directory = await getApplicationDocumentsDirectory();
    return path.join(directory.path, Configuration.databaseName);
  }

  /// Close the database
  Future<void> close() async {
    await _database?.close();
    _database = null;
  }

  /// Clear all events (for testing)
  Future<void> clearAllEvents() async {
    await database.delete('events');
  }

  /// Get event count
  Future<int> getEventCount() async {
    final result =
        await database.rawQuery('SELECT COUNT(*) as count FROM events');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Get synced event count
  Future<int> getSyncedEventCount() async {
    final result = await database
        .rawQuery('SELECT COUNT(*) as count FROM events WHERE synced = 1');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Get unsynced event count
  Future<int> getUnsyncedEventCount() async {
    final result = await database
        .rawQuery('SELECT COUNT(*) as count FROM events WHERE synced = 0');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  /// Delete old events (keep last N events)
  Future<void> deleteOldEvents({int keepCount = 1000}) async {
    await database.rawDelete('''
      DELETE FROM events
      WHERE id NOT IN (
        SELECT id FROM events
        ORDER BY timestamp DESC
        LIMIT ?
      )
    ''', [keepCount]);
  }

  /// Get profile information
  Future<Map<String, dynamic>?> getProfile() async {
    final result = await database.query('profiles', limit: 1);
    return result.isNotEmpty ? result.first : null;
  }

  /// Update profile information
  Future<void> updateProfile({
    String? displayName,
    String? node,
    String? tailscaleId,
    String? tailscaleToken,
  }) async {
    final updates = <String, dynamic>{};

    if (displayName != null) updates['display_name'] = displayName;
    if (node != null) updates['node'] = node;
    if (tailscaleId != null) updates['tailscale_id'] = tailscaleId;
    if (tailscaleToken != null) updates['tailscale_token'] = tailscaleToken;

    if (updates.isNotEmpty) {
      await database.update('profiles', updates, where: 'id = 1');
    }
  }

  /// Delete database file and recreate with latest schema
  Future<void> wipeAllData() async {
    // Close current database connection
    await close();

    // Get database file path
    final directory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(directory.path, Configuration.databaseName);
    final dbFile = File(dbPath);

    // Delete the database file if it exists
    if (await dbFile.exists()) {
      await dbFile.delete();
      print('Database file deleted: $dbPath');
    }

    // Reinitialize database (this will recreate it with latest schema)
    await initialize();
    print('Database recreated with latest schema');
  }
}
